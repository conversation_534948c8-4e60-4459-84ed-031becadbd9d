import time
import ctypes
from ctypes import wintypes
import sys
import threading

# Windows API constants
MOUSEEVENTF_LEFTDOWN = 0x0002
MOUSEEVENTF_LEFTUP = 0x0004

# Load user32.dll
user32 = ctypes.windll.user32

class FastMouseClicker:
    def __init__(self):
        self.clicking = False
        self.click_count = 0
        
    def click_mouse(self):
        """Simulate a mouse left click at current cursor position"""
        user32.mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
        time.sleep(0.01)  # 10ms between down and up
        user32.mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)
        self.click_count += 1

    def get_cursor_pos(self):
        """Get current cursor position"""
        point = wintypes.POINT()
        user32.GetCursorPos(ctypes.byref(point))
        return point.x, point.y

    def start_clicking(self, interval):
        """Start the clicking loop"""
        self.clicking = True
        last_report_time = time.time()
        last_report_count = 0
        
        while self.clicking:
            try:
                self.click_mouse()
                
                # Report progress every second
                current_time = time.time()
                if current_time - last_report_time >= 1.0:
                    clicks_per_sec = self.click_count - last_report_count
                    print(f"Total clicks: {self.click_count} | Speed: {clicks_per_sec} clicks/sec | Time: {time.strftime('%H:%M:%S')}")
                    last_report_time = current_time
                    last_report_count = self.click_count
                
                time.sleep(interval)
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"Error: {e}")
                break
        
        self.clicking = False

    def stop_clicking(self):
        """Stop the clicking"""
        self.clicking = False

def main():
    print("=" * 50)
    print("         Fast Mouse Auto Click Tool")
    print("=" * 50)
    print()
    print("Instructions:")
    print("1. Move mouse to target position")
    print("2. Select click speed")
    print("3. Press Enter to start clicking")
    print("4. Press Ctrl+C to stop")
    print("=" * 50)
    print()
    
    # Speed presets
    speed_options = {
        "1": (0.025, "Extreme Fast (40 clicks/sec)"),
        "2": (0.05, "Very Fast (20 clicks/sec)"),
        "3": (0.1, "Fast (10 clicks/sec)"),
        "4": (0.2, "Medium (5 clicks/sec)"),
        "5": (0.5, "Normal (2 clicks/sec)"),
        "6": (1.0, "Slow (1 click/sec)"),
        "7": (None, "Custom interval")
    }
    
    print("Click speed presets:")
    for key, (interval, description) in speed_options.items():
        print(f"{key}. {description}")
    print()
    
    # Get user choice
    choice = input("Select speed (1-7): ").strip()
    
    if choice in speed_options:
        interval, description = speed_options[choice]
        if interval is None:  # Custom option
            try:
                interval = float(input("Enter custom interval in seconds (e.g., 0.01 for 100 clicks/sec): "))
                description = f"Custom ({1/interval:.1f} clicks/sec)"
            except ValueError:
                interval = 0.1
                description = "Custom (10 clicks/sec) - invalid input, using default"
    else:
        interval = 0.1
        description = "Fast (10 clicks/sec) - invalid choice, using default"
    
    print(f"Selected: {description}")
    print()
    
    # Initialize clicker
    clicker = FastMouseClicker()
    
    # Get initial cursor position
    x, y = clicker.get_cursor_pos()
    print(f"Target position: X={x}, Y={y}")
    print(f"Click interval: {interval} seconds")
    print()
    
    print("Move mouse to target position and press Enter...")
    input()
    
    print(f"Starting fast auto click...")
    print("Press Ctrl+C to stop")
    print()
    
    try:
        clicker.start_clicking(interval)
    except KeyboardInterrupt:
        clicker.stop_clicking()
        print(f"\nStopped after {clicker.click_count} clicks")
        print("Script terminated by user")
    except Exception as e:
        print(f"Unexpected error: {e}")
    
    print("Script finished")

if __name__ == "__main__":
    main()
