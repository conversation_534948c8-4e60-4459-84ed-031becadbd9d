@echo off
title Fast Mouse Auto Click Tool

echo ========================================
echo       Fast Mouse Auto Click Tool
echo ========================================
echo.
echo Instructions:
echo 1. Move mouse to target position
echo 2. Select click speed
echo 3. Press Enter to start clicking
echo 4. Press Ctrl+C to stop
echo ========================================
echo.

echo Click speed presets:
echo 1. Extreme Fast (50ms = 20 clicks/sec)
echo 2. Very Fast (100ms = 10 clicks/sec)
echo 3. Fast (200ms = 5 clicks/sec)
echo 4. Medium (500ms = 2 clicks/sec)
echo 5. Normal (1000ms = 1 click/sec)
echo 6. Custom (enter your own milliseconds)
echo.

set /p choice="Select speed (1-6): "

if "%choice%"=="1" (
    set interval_ms=50
    echo Selected: Extreme Fast - 20 clicks per second
)
if "%choice%"=="2" (
    set interval_ms=100
    echo Selected: Very Fast - 10 clicks per second
)
if "%choice%"=="3" (
    set interval_ms=200
    echo Selected: Fast - 5 clicks per second
)
if "%choice%"=="4" (
    set interval_ms=500
    echo Selected: Medium - 2 clicks per second
)
if "%choice%"=="5" (
    set interval_ms=1000
    echo Selected: Normal - 1 click per second
)
if "%choice%"=="6" (
    set /p interval_ms="Enter interval in milliseconds (e.g., 25 for very fast): "
    if "%interval_ms%"=="" set interval_ms=100
    echo Selected: Custom - %interval_ms%ms interval
)

if "%interval_ms%"=="" (
    set interval_ms=1000
    echo No selection, using Normal speed
)

echo.
echo Move mouse to target position and press Enter...
pause >nul

echo.
echo Starting fast auto click every %interval_ms%ms...
echo Press Ctrl+C to stop
echo.

rem Create PowerShell script for fast clicking
echo Add-Type -AssemblyName System.Windows.Forms > fastclick.ps1
echo Add-Type -AssemblyName System.Drawing >> fastclick.ps1
echo. >> fastclick.ps1
echo $signature = @' >> fastclick.ps1
echo [DllImport("user32.dll",CharSet=CharSet.Auto, CallingConvention=CallingConvention.StdCall)] >> fastclick.ps1
echo public static extern void mouse_event(long dwFlags, long dx, long dy, long cButtons, long dwExtraInfo); >> fastclick.ps1
echo '@ >> fastclick.ps1
echo. >> fastclick.ps1
echo $SendMouseClick = Add-Type -memberDefinition $signature -name "Win32MouseEventNew" -namespace Win32Functions -passThru >> fastclick.ps1
echo. >> fastclick.ps1
echo $count = 0 >> fastclick.ps1
echo while ($true) { >> fastclick.ps1
echo     try { >> fastclick.ps1
echo         # Left mouse button down >> fastclick.ps1
echo         $SendMouseClick::mouse_event(0x00000002, 0, 0, 0, 0) >> fastclick.ps1
echo         Start-Sleep -Milliseconds 20 >> fastclick.ps1
echo         # Left mouse button up >> fastclick.ps1
echo         $SendMouseClick::mouse_event(0x00000004, 0, 0, 0, 0) >> fastclick.ps1
echo         $count++ >> fastclick.ps1
echo         if ($count %% 10 -eq 0) { >> fastclick.ps1
echo             Write-Host "Clicks: $count - $(Get-Date -Format 'HH:mm:ss')" >> fastclick.ps1
echo         } >> fastclick.ps1
echo         Start-Sleep -Milliseconds %interval_ms% >> fastclick.ps1
echo     } catch { >> fastclick.ps1
echo         Write-Host "Error: $_" >> fastclick.ps1
echo         break >> fastclick.ps1
echo     } >> fastclick.ps1
echo } >> fastclick.ps1

powershell.exe -ExecutionPolicy Bypass -File fastclick.ps1

echo.
echo Cleaning up...
del fastclick.ps1 2>nul
echo Script stopped
pause
