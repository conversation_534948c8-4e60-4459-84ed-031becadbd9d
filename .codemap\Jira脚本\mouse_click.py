import time
import ctypes
from ctypes import wintypes
import sys

# Windows API constants
MOUSEEVENTF_LEFTDOWN = 0x0002
MOUSEEVENTF_LEFTUP = 0x0004

# Load user32.dll
user32 = ctypes.windll.user32

def click_mouse():
    """Simulate a mouse left click at current cursor position"""
    user32.mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0)
    time.sleep(0.1)
    user32.mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0)

def get_cursor_pos():
    """Get current cursor position"""
    point = wintypes.POINT()
    user32.GetCursorPos(ctypes.byref(point))
    return point.x, point.y

def main():
    print("=" * 40)
    print("         Mouse Auto Click Tool")
    print("=" * 40)
    print()
    print("Instructions:")
    print("1. Move mouse to target position")
    print("2. Press Enter to start clicking")
    print("3. Press Ctrl+C to stop")
    print("=" * 40)
    print()
    
    # Get click interval
    print("Click interval options:")
    print("1. Ultra Fast (0.1 seconds)")
    print("2. Very Fast (0.2 seconds)")
    print("3. Fast (0.5 seconds)")
    print("4. Normal (1 second)")
    print("5. Slow (2 seconds)")
    print("6. Custom interval")
    print()

    try:
        choice = input("Select option (1-6) or press Enter for Normal: ").strip()
        if choice == "1":
            interval = 0.1
        elif choice == "2":
            interval = 0.2
        elif choice == "3":
            interval = 0.5
        elif choice == "4" or choice == "":
            interval = 1.0
        elif choice == "5":
            interval = 2.0
        elif choice == "6":
            custom = input("Enter custom interval in seconds (e.g., 0.05 for very fast): ").strip()
            interval = float(custom) if custom else 1.0
        else:
            interval = 1.0
            print("Invalid choice, using default interval of 1 second")
    except ValueError:
        interval = 1.0
        print("Invalid input, using default interval of 1 second")
    
    print()
    print("Move mouse to target position and press Enter...")
    input()
    
    # Get initial cursor position
    x, y = get_cursor_pos()
    print(f"Target position: X={x}, Y={y}")
    print(f"Starting auto click every {interval} seconds...")
    print("Press Ctrl+C to stop")
    print()
    
    count = 0
    try:
        while True:
            click_mouse()
            count += 1
            current_time = time.strftime("%H:%M:%S")
            print(f"Click {count} at {current_time}")
            time.sleep(interval)
    except KeyboardInterrupt:
        print(f"\nStopped after {count} clicks")
        print("Script terminated by user")

if __name__ == "__main__":
    main()
