@echo off
title Mouse Auto Click Tool

echo ========================================
echo         Mouse Auto Click Tool
echo ========================================
echo.
echo Instructions:
echo 1. Move mouse to target position
echo 2. Press Enter to start clicking
echo 3. Press Ctrl+C to stop
echo ========================================
echo.

echo Click speed options:
echo 1. Ultra Fast (0.1 seconds)
echo 2. Very Fast (0.2 seconds)
echo 3. Fast (0.5 seconds)
echo 4. Normal (1 second)
echo 5. Slow (2 seconds)
echo 6. Custom interval
echo.

set /p choice="Select option (1-6) or press Enter for Normal: "

if "%choice%"=="1" set interval=0.1
if "%choice%"=="2" set interval=0.2
if "%choice%"=="3" set interval=0.5
if "%choice%"=="4" set interval=1
if "%choice%"=="5" set interval=2
if "%choice%"=="6" (
    set /p interval="Enter custom interval in seconds: "
    if "!interval!"=="" set interval=1
)
if "%choice%"=="" set interval=1

echo Selected interval: %interval% seconds

echo.
echo Move mouse to target position and press Enter...
pause >nul

echo.
echo Starting auto click every %interval% seconds...
echo Press Ctrl+C to stop
echo.

set count=0

:loop
set /a count+=1
echo Click %count% at %time%

rem Use PowerShell for more precise clicking and timing
powershell.exe -Command ^
"Add-Type -AssemblyName System.Windows.Forms; ^
$signature = '[DllImport(\"user32.dll\",CharSet=CharSet.Auto,CallingConvention=CallingConvention.StdCall)] public static extern void mouse_event(long dwFlags, long dx, long dy, long cButtons, long dwExtraInfo);'; ^
$SendMouseClick = Add-Type -memberDefinition $signature -name \"Win32MouseEventNew\" -namespace Win32Functions -passThru; ^
$SendMouseClick::mouse_event(0x00000002, 0, 0, 0, 0); ^
Start-Sleep -Milliseconds 50; ^
$SendMouseClick::mouse_event(0x00000004, 0, 0, 0, 0); ^
Start-Sleep -Seconds %interval%"

goto loop
